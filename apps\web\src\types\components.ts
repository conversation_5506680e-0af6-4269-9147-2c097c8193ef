import React from 'react';

// Fix for React 19 JSX component types
export type IconComponent = React.ComponentType<{
  className?: string;
}>;

export type NavigationItem = {
  name: string;
  href: string;
  icon: IconComponent;
};

export type NavigationCategory = {
  name: string;
  icon: IconComponent;
  items: NavigationItem[];
};

// Type assertion helper for any icon component
export const asIconComponent = (icon: any): IconComponent => icon as IconComponent;
