const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

// Find the project and workspace directories
const projectRoot = __dirname;
const monorepoRoot = path.resolve(projectRoot, '../..');

const config = getDefaultConfig(projectRoot);

// 1. Watch both project and monorepo for dependency resolution
config.watchFolders = [monorepoRoot];

// 2. Use both local and root node_modules for dependency resolution
config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
  path.resolve(monorepoRoot, 'node_modules'),
];

// 3. Force Metro to resolve (sub)dependencies only from the `nodeModulesPaths`
config.resolver.disableHierarchicalLookup = true;

// 4. React 19 compatibility - ensure React is resolved from mobile app
config.resolver.alias = {
  'react': path.resolve(projectRoot, 'node_modules/react'),
  'react-dom': path.resolve(projectRoot, 'node_modules/react-dom'),
  'react-native': path.resolve(projectRoot, 'node_modules/react-native'),
  'react-native-reanimated': path.resolve(projectRoot, 'node_modules/react-native-reanimated'),
};

// 5. Basic configuration without NativeWind (fallback)
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// 6. ENABLE cache for better performance
config.resetCache = false;

// 7. Fix file watcher issues on Windows
config.watcher = {
  healthCheck: {
    enabled: true,
    filePrefix: '.metro-health-check',
    timeout: 30000,
  },
  watchman: {
    deferStates: ['hg.update'],
  },
};

console.log('📝 Using FIXED Metro config (File watcher optimized)');

module.exports = config;
