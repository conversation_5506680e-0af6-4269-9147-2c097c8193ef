{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**", "build/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^build"]}, "type-check": {"dependsOn": ["^build"]}, "clean": {"cache": false}, "ios": {"cache": false, "persistent": true}, "android": {"cache": false, "persistent": true}}}